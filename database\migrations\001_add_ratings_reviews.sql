-- Migration: Add ratings and reviews tables
-- This file should be run in your Supabase SQL editor

-- Create ratings table
CREATE TABLE IF NOT EXISTS ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(recipe_id, user_id)
);

-- Create reviews table
CREATE TABLE IF NOT EXISTS reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  rating_id UUID REFERENCES ratings(id) ON DELETE CASCADE,
  title VARCHAR(255),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create collections table
CREATE TABLE IF NOT EXISTS collections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create collection_recipes junction table
CREATE TABLE IF NOT EXISTS collection_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
  recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(collection_id, recipe_id)
);

-- Add nutrition columns to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS nutrition_calories INTEGER,
ADD COLUMN IF NOT EXISTS nutrition_protein DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS nutrition_carbs DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS nutrition_fat DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS nutrition_fiber DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS nutrition_sugar DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS nutrition_sodium DECIMAL(5,2);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ratings_recipe_id ON ratings(recipe_id);
CREATE INDEX IF NOT EXISTS idx_ratings_user_id ON ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_recipe_id ON reviews(recipe_id);
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_collections_user_id ON collections(user_id);
CREATE INDEX IF NOT EXISTS idx_collection_recipes_collection_id ON collection_recipes(collection_id);
CREATE INDEX IF NOT EXISTS idx_collection_recipes_recipe_id ON collection_recipes(recipe_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_ratings_updated_at BEFORE UPDATE ON ratings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON collections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE collection_recipes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Ratings policies
CREATE POLICY "Users can view all ratings" ON ratings FOR SELECT USING (true);
CREATE POLICY "Users can insert their own ratings" ON ratings FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their own ratings" ON ratings FOR UPDATE USING (true);
CREATE POLICY "Users can delete their own ratings" ON ratings FOR DELETE USING (true);

-- Reviews policies
CREATE POLICY "Users can view all reviews" ON reviews FOR SELECT USING (true);
CREATE POLICY "Users can insert their own reviews" ON reviews FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their own reviews" ON reviews FOR UPDATE USING (true);
CREATE POLICY "Users can delete their own reviews" ON reviews FOR DELETE USING (true);

-- Collections policies
CREATE POLICY "Users can view public collections and their own" ON collections FOR SELECT USING (is_public = true OR true);
CREATE POLICY "Users can insert their own collections" ON collections FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their own collections" ON collections FOR UPDATE USING (true);
CREATE POLICY "Users can delete their own collections" ON collections FOR DELETE USING (true);

-- Collection recipes policies
CREATE POLICY "Users can view collection recipes for accessible collections" ON collection_recipes FOR SELECT USING (true);
CREATE POLICY "Users can manage their own collection recipes" ON collection_recipes FOR ALL USING (true);
