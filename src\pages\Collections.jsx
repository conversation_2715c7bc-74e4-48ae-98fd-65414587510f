import { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FolderO<PERSON>, 
  Folder, 
  Eye, 
  EyeOff, 
  Clock, 
  Users, 
  ChefHat,
  Search,
  Grid,
  List,
  ArrowLeft,
  Share2,
  Edit,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { auth } from '@/config/firebase';
import supabase from '@/config/supabase';
import { formatDate } from '@/lib/utils';
import Collections from '@/components/Collections';

const CollectionsPage = () => {
  const { id } = useParams();
  const [collections, setCollections] = useState([]);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [collectionRecipes, setCollectionRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [recipesLoading, setRecipesLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [filteredRecipes, setFilteredRecipes] = useState([]);

  const currentUser = auth.currentUser;

  useEffect(() => {
    fetchCollections();
  }, [currentUser]);

  useEffect(() => {
    if (id) {
      fetchCollectionById(id);
    }
  }, [id]);

  useEffect(() => {
    if (selectedCollection) {
      fetchCollectionRecipes(selectedCollection.id);
    }
  }, [selectedCollection]);

  useEffect(() => {
    // Filter recipes based on search query
    if (searchQuery.trim()) {
      const filtered = collectionRecipes.filter(recipe =>
        recipe.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recipe.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recipe.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredRecipes(filtered);
    } else {
      setFilteredRecipes(collectionRecipes);
    }
  }, [searchQuery, collectionRecipes]);

  const fetchCollections = async () => {
    if (!currentUser) {
      setLoading(false);
      return;
    }

    try {
      const userMappings = JSON.parse(localStorage.getItem('userMappings') || '{}');
      const userUuid = userMappings[currentUser.uid];
      
      if (!userUuid) {
        setLoading(false);
        return;
      }

      // Fetch user's collections and public collections
      const { data, error } = await supabase
        .from('collections')
        .select(`
          *,
          collection_recipes (
            recipe_id
          )
        `)
        .or(`user_id.eq.${userUuid},is_public.eq.true`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCollections(data || []);
    } catch (error) {
      console.error('Error fetching collections:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCollectionById = async (collectionId) => {
    try {
      const { data, error } = await supabase
        .from('collections')
        .select(`
          *,
          collection_recipes (
            recipe_id
          )
        `)
        .eq('id', collectionId)
        .single();

      if (error) throw error;
      setSelectedCollection(data);
    } catch (error) {
      console.error('Error fetching collection:', error);
    }
  };

  const fetchCollectionRecipes = async (collectionId) => {
    setRecipesLoading(true);
    try {
      const { data, error } = await supabase
        .from('collection_recipes')
        .select(`
          recipe_id,
          recipes (
            id,
            title,
            description,
            cook_time,
            servings,
            difficulty,
            tags,
            image_path,
            created_at,
            user_id
          )
        `)
        .eq('collection_id', collectionId);

      if (error) throw error;

      const recipes = [];
      for (const item of data || []) {
        if (item.recipes) {
          // Fetch image URL from Supabase storage
          let imageUrl = null;
          if (item.recipes.image_path) {
            try {
              const { data: imageData } = await supabase
                .storage
                .from('recipe-images')
                .getPublicUrl(item.recipes.image_path);

              if (imageData) {
                imageUrl = imageData.publicUrl;
              }
            } catch (err) {
              console.error('Error getting image URL:', err);
            }
          }

          recipes.push({
            ...item.recipes,
            imageUrl: imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'
          });
        }
      }

      setCollectionRecipes(recipes);
    } catch (error) {
      console.error('Error fetching collection recipes:', error);
    } finally {
      setRecipesLoading(false);
    }
  };

  const handleDeleteCollection = async (collectionId) => {
    if (!window.confirm('Are you sure you want to delete this collection?')) return;

    try {
      const { error } = await supabase
        .from('collections')
        .delete()
        .eq('id', collectionId);

      if (error) throw error;
      
      // If we're viewing the deleted collection, go back to collections list
      if (selectedCollection?.id === collectionId) {
        setSelectedCollection(null);
        window.history.pushState({}, '', '/collections');
      }
      
      fetchCollections();
    } catch (error) {
      console.error('Error deleting collection:', error);
    }
  };

  const isOwner = (collection) => {
    if (!currentUser) return false;
    const userMappings = JSON.parse(localStorage.getItem('userMappings') || '{}');
    const userUuid = userMappings[currentUser.uid];
    return collection.user_id === userUuid;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="border rounded-lg p-6">
                <div className="h-6 bg-muted rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-muted rounded w-full mb-2"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // If viewing a specific collection
  if (selectedCollection) {
    return (
      <div className="space-y-6">
        {/* Collection Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedCollection(null);
                window.history.pushState({}, '', '/collections');
              }}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Collections
            </Button>
          </div>
        </div>

        <div className="border rounded-lg p-6 bg-card">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              {selectedCollection.is_public ? (
                <FolderOpen className="h-8 w-8 text-primary" />
              ) : (
                <Folder className="h-8 w-8 text-muted-foreground" />
              )}
              <div>
                <h1 className="text-2xl font-bold">{selectedCollection.name}</h1>
                {selectedCollection.description && (
                  <p className="text-muted-foreground mt-1">{selectedCollection.description}</p>
                )}
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <span>{collectionRecipes.length} recipe{collectionRecipes.length !== 1 ? 's' : ''}</span>
                  <span>Created {formatDate(selectedCollection.created_at)}</span>
                  {selectedCollection.is_public && (
                    <span className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      Public
                    </span>
                  )}
                </div>
              </div>
            </div>

            {isOwner(selectedCollection) && (
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteCollection(selectedCollection.id)}
                  className="flex items-center gap-2 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Search and View Controls */}
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <input
              type="text"
              placeholder="Search recipes in this collection..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/30"
            />
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="flex items-center gap-2"
            >
              <Grid className="h-4 w-4" />
              Grid
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="flex items-center gap-2"
            >
              <List className="h-4 w-4" />
              List
            </Button>
          </div>
        </div>

        {/* Recipes */}
        {recipesLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse border rounded-lg p-4">
                <div className="h-48 bg-muted rounded mb-4"></div>
                <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-full"></div>
              </div>
            ))}
          </div>
        ) : filteredRecipes.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            {searchQuery ? (
              <p>No recipes found matching "{searchQuery}"</p>
            ) : (
              <p>This collection is empty.</p>
            )}
          </div>
        ) : (
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
          }>
            {filteredRecipes.map((recipe) => (
              <motion.div
                key={recipe.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={viewMode === 'grid' 
                  ? "border rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
                  : "border rounded-lg p-4 hover:shadow-md transition-shadow"
                }
              >
                <Link to={`/recipes/${recipe.id}`}>
                  {viewMode === 'grid' ? (
                    <>
                      <div className="aspect-video overflow-hidden">
                        <img
                          src={recipe.imageUrl}
                          alt={recipe.title}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold mb-2 line-clamp-2">{recipe.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {recipe.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {recipe.cook_time} min
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            {recipe.servings}
                          </span>
                          <span className="flex items-center gap-1">
                            <ChefHat className="h-4 w-4" />
                            {recipe.difficulty}
                          </span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex gap-4">
                      <div className="w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg">
                        <img
                          src={recipe.imageUrl}
                          alt={recipe.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold mb-1">{recipe.title}</h3>
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                          {recipe.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {recipe.cook_time} min
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            {recipe.servings}
                          </span>
                          <span className="flex items-center gap-1">
                            <ChefHat className="h-4 w-4" />
                            {recipe.difficulty}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </Link>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Collections list view
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Recipe Collections</h1>
      </div>

      {/* Collections Component */}
      <Collections onCollectionUpdate={fetchCollections} />

      {/* Collections Grid */}
      {collections.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">
            {currentUser ? 'Your Collections & Public Collections' : 'Public Collections'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {collections.map((collection) => (
              <motion.div
                key={collection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border rounded-lg p-6 bg-card hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => {
                  setSelectedCollection(collection);
                  window.history.pushState({}, '', `/collections/${collection.id}`);
                }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {collection.is_public ? (
                      <FolderOpen className="h-6 w-6 text-primary" />
                    ) : (
                      <Folder className="h-6 w-6 text-muted-foreground" />
                    )}
                    <div>
                      <h3 className="font-semibold">{collection.name}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        {collection.is_public ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )}
                        <span>{collection.is_public ? 'Public' : 'Private'}</span>
                      </div>
                    </div>
                  </div>
                  
                  {isOwner(collection) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteCollection(collection.id);
                      }}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                {collection.description && (
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {collection.description}
                  </p>
                )}

                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>
                    {collection.collection_recipes?.length || 0} recipe{(collection.collection_recipes?.length || 0) !== 1 ? 's' : ''}
                  </span>
                  <span>{formatDate(collection.created_at)}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CollectionsPage;
