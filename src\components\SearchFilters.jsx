import { useState } from 'react';
import { Search, Filter, X, Clock, ChefHat, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';

const SearchFilters = ({ onSearch, onFiltersChange, initialFilters = {} }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    difficulty: '',
    maxCookTime: '',
    cuisine: '',
    dietary: '',
    ...initialFilters
  });

  const difficulties = ['Easy', 'Medium', 'Hard'];
  const cookTimes = [
    { label: '15 min', value: 15 },
    { label: '30 min', value: 30 },
    { label: '1 hour', value: 60 },
    { label: '2+ hours', value: 120 }
  ];
  const cuisines = [
    'Italian', 'Mexican', 'Chinese', 'Indian', 'American', 
    'French', 'Japanese', 'Thai', 'Mediterranean', 'Korean'
  ];
  const dietaryOptions = [
    'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 
    'Keto', 'Low-Carb', 'High-Protein', 'Paleo'
  ];

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch(value);
  };

  const handleFilterChange = (filterType, value) => {
    const newFilters = {
      ...filters,
      [filterType]: filters[filterType] === value ? '' : value
    };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      difficulty: '',
      maxCookTime: '',
      cuisine: '',
      dietary: ''
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
        <input
          type="text"
          placeholder="Search recipes by name, description, or ingredients..."
          value={searchQuery}
          onChange={handleSearchChange}
          className="w-full pl-10 pr-4 py-3 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/30"
        />
      </div>

      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          Filters
          {activeFiltersCount > 0 && (
            <span className="bg-primary text-primary-foreground text-xs rounded-full px-2 py-1">
              {activeFiltersCount}
            </span>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear all
          </Button>
        )}
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="border rounded-lg p-4 bg-card space-y-6"
          >
            {/* Difficulty Filter */}
            <div>
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <ChefHat className="h-4 w-4" />
                Difficulty
              </h3>
              <div className="flex flex-wrap gap-2">
                {difficulties.map((difficulty) => (
                  <Button
                    key={difficulty}
                    variant={filters.difficulty === difficulty ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange('difficulty', difficulty)}
                  >
                    {difficulty}
                  </Button>
                ))}
              </div>
            </div>

            {/* Cook Time Filter */}
            <div>
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Max Cook Time
              </h3>
              <div className="flex flex-wrap gap-2">
                {cookTimes.map((time) => (
                  <Button
                    key={time.value}
                    variant={filters.maxCookTime === time.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange('maxCookTime', time.value)}
                  >
                    {time.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Cuisine Filter */}
            <div>
              <h3 className="font-medium mb-3">Cuisine</h3>
              <div className="flex flex-wrap gap-2">
                {cuisines.map((cuisine) => (
                  <Button
                    key={cuisine}
                    variant={filters.cuisine === cuisine ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange('cuisine', cuisine)}
                  >
                    {cuisine}
                  </Button>
                ))}
              </div>
            </div>

            {/* Dietary Filter */}
            <div>
              <h3 className="font-medium mb-3">Dietary</h3>
              <div className="flex flex-wrap gap-2">
                {dietaryOptions.map((dietary) => (
                  <Button
                    key={dietary}
                    variant={filters.dietary === dietary ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange('dietary', dietary)}
                  >
                    {dietary}
                  </Button>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchFilters;
